import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:url_launcher/url_launcher.dart';

class UpdatePage extends StatelessWidget {
  const UpdatePage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Container(
            padding: const EdgeInsets.all(15),
            alignment: Alignment.center,
            child: Column(children: <Widget>[_formUI(context)])),
      ),
    );
  }

  _formUI(BuildContext context) {
    double height = MediaQuery.of(context).size.height;
    double width = MediaQuery.of(context).size.width;
    return Container(
      width: 400,
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          SizedBox(
            height: height / 15,
          ),
          SizedBox(
            height: height / 3.9,
            width: width / 2,
            child: Image.asset(logoImage),
          ),
          SizedBox(height: height / 33),
          Text('PoleVault', style: Theme.of(context).textTheme.headlineSmall),
          SizedBox(height: height / 12),
          const SizedBox(height: 20.0),
          const Align(
            alignment: Alignment.center,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 30),
              child: Text(
                'Kindly upgrade to the latest version from the Play Store to continue using this app!',
                style: TextStyle(
                  fontSize: 18,
                ),
              ),
            ),
          ),
          const SizedBox(height: 80.0),
          ElevatedButton(
            style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all(
                  const Color.fromARGB(248, 64, 124, 161)),
              foregroundColor: MaterialStateProperty.all(Colors.white),
              shape: MaterialStateProperty.all(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30.0))),
            ),
            child: Text("Update".toUpperCase()),
            onPressed: () {
              launchUrl();
            },
          ),
        ],
      ),
    );
  }

  Future<void> launchUrl() async {
    try {
      launch(
          "https://play.google.com/store/apps/details?id=com.schnelliot.polevault");
    } on PlatformException catch (e) {
      launch(
          "https://play.google.com/store/apps/details?id=com.schnelliot.polevault");
    } finally {
      launch(
          "https://play.google.com/store/apps/details?id=com.schnelliot.polevault");
    }
  }
}
