import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/dio_client.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:aws_s3_upload/aws_s3_upload.dart';
import 'package:schnell_pole_installation/utils/s3_config.dart';

class SurveyService {
  var status;

  Future<String> updateSurveyImageServiceS3(
      String base64Image, String fileName, BuildContext context) async {
    try {
      log('Starting S3 upload for file: $fileName');

      // Convert base64 to bytes
      final bytes = base64Decode(base64Image);
      log('Base64 decoded successfully, bytes length: ${bytes.length}');

      // Create temporary file
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$fileName');
      await tempFile.writeAsBytes(bytes);
      log('Temporary file created: ${tempFile.path}');

      // Verify file exists and has content
      if (!await tempFile.exists()) {
        log('Error: Temporary file does not exist');
        return "400";
      }

      final fileSize = await tempFile.length();
      log('File size: $fileSize bytes');

      if (fileSize == 0) {
        log('Error: File is empty');
        return "400";
      }

      log('Uploading to S3 with config:');
      log('Bucket: ${S3Config.bucketName}');
      log('Region: ${S3Config.region}');
      log('Key: ${S3Config.luminatorFolder}/$fileName');

      final result = await AwsS3.uploadFile(
        accessKey: S3Config.accessKey,
        secretKey: S3Config.secretKey,
        file: tempFile,
        bucket: S3Config.bucketName,
        region: S3Config.region,
        key: '${S3Config.luminatorFolder}/$fileName',
        metadata: {
          'activity': 'SURVEY',
          'uploaded_at': DateTime.now().toIso8601String(),
        },
      );

      log('S3 upload result: $result');
      log('S3 upload result type: ${result.runtimeType}');

      // Clean up temporary file
      try {
        await tempFile.delete();
        log('Temporary file deleted');
      } catch (e) {
        log('Warning: Could not delete temporary file: $e');
      }

      // Check if upload was successful
      // The aws_s3_upload package typically returns a String URL on success or null on failure
      if (result != null && result.toString().isNotEmpty) {
        log('S3 upload successful, URL: $result');
        return "200";
      } else {
        log('S3 upload failed - result is null or empty');
        return "400";
      }
    } catch (e) {
      log('S3 upload error: $e');
      log('S3 upload error type: ${e.runtimeType}');
      return 'Something Went Wrong';
    }
  }

  Future<String> updateSurveyImageService(
      deviceImage, fileName, context) async {
    var data = {
      "name": fileName,
      "activity": "SURVEY",
      "auditImg": deviceImage
    };
    Dio dio = DioClient.dio;
    try {
      Response response = await dio.post('$baseUrl/api/image/upload/',
          data: data,
          options: Options(contentType: 'application/json', headers: {
            // "token": token,
          }));
      if (response.data != "") {
        var result = jsonDecode(response.data);
        status = result['status'].toString();
      } else {
        //  snackBarLoader('No Data Found',context);
      }
      return status;
    } catch (e) {
      return 'Something Went Wrong';
    }
  }

  Future<String> updateSurveyDetailService(context, data) async {
    log('postdata: $data');
    Dio dio = DioClient.dio;
    var url = '';
    if (data['assetType'] == 'Pole') {
      url = '$baseUrl/api/pole/install/';
    } else if (data['assetType'] == 'Switch Point') {
      url = '$baseUrl/api/switch_point/install/';
    } else if (data['assetType'] == 'Transformer') {
      url = '$baseUrl/api/transformer/install/';
    } else {
      url = '$baseUrl/api/pole/install/';
    }
    // var url = '$baseUrl/api/pole/install/';
    try {
      Response response = await dio.post(url,
          data: data,
          options: Options(contentType: 'application/json', headers: {
            // "token": token,
          }));
      if (response.data != "") {
        var result = jsonDecode(response.data);
        log('$result');
        status = result['status'].toString();
      }
      return status;
    } catch (e) {
      log('$e');
      return 'Something Went Wrong';
    }
  }
}
