import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as r;
import 'package:schnell_pole_installation/survey/survey_controller.dart';
import 'package:schnell_pole_installation/utils/constants.dart';

class CaptureThreeImagesScreen extends r.ConsumerStatefulWidget {
  const CaptureThreeImagesScreen({super.key});

  @override
  r.ConsumerState<CaptureThreeImagesScreen> createState() =>
      _CaptureThreeImagesScreenState();
}

class _CaptureThreeImagesScreenState
    extends r.ConsumerState<CaptureThreeImagesScreen> {
  late CameraController _cameraController;
  late List<CameraDescription> _cameras;
  bool _isCameraInitialized = false;

  final List<XFile?> _capturedImages = List.filled(3, null);

  int _nextImageIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    _cameras = await availableCameras();
    _cameraController =
        CameraController(_cameras.first, ResolutionPreset.medium);
    await _cameraController.initialize();
    setState(() => _isCameraInitialized = true);
  }

  Future<void> _captureImage() async {
    if (!_cameraController.value.isInitialized ||
        _cameraController.value.isTakingPicture) return;

    if (_nextImageIndex >= 3) return;

    try {
      final photo = await _cameraController.takePicture();
      setState(() {
        _capturedImages[_nextImageIndex] = photo;
        _nextImageIndex++;
      });
    } catch (e) {
      print("Capture error: $e");
    }
  }

  void _retakeImage(int index) async {
    try {
      final photo = await _cameraController.takePicture();
      setState(() {
        _capturedImages[index] = photo;
      });
    } catch (e) {
      print("Retake error: $e");
    }
  }

  Widget _buildImageGrid(int index) {
    final image = _capturedImages[index];
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        color: Colors.black12,
      ),
      child: Stack(
        children: [
          image != null
              ? Positioned.fill(
                  child: Image.file(File(image.path), fit: BoxFit.cover),
                )
              : const Center(
                  child: Icon(Icons.camera_alt, size: 36, color: Colors.grey)),
          if (image != null)
            Positioned(
              bottom: 3,
              right: 3,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  shape: const CircleBorder(),
                  // padding: const EdgeInsets.all(8),
                  backgroundColor: Colors.black54,
                  foregroundColor: Colors.white, // icon color
                  elevation: 2,
                ),
                onPressed: () => _retakeImage(index),
                child: const Icon(Icons.replay_outlined, size: 20),
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _cameraController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String clickEventIsFor = ref.watch(surveyController).clickEventIsFor;
    return WillPopScope(
      onWillPop: () => Future.value(false),
      child: SafeArea(
        child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            title: const Text('Capture the Picture'),
            backgroundColor: lightBlue,
          ),
          body: _isCameraInitialized
              ? Column(
                  children: [
                    SizedBox(
                        height: MediaQuery.of(context).size.height * 0.6,
                        child: CameraPreview(_cameraController)),
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: List.generate(
                          3,
                          (index) => Expanded(
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 4),
                              child: AspectRatio(
                                aspectRatio: 1,
                                child: _buildImageGrid(index),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Center(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              shape: const CircleBorder(),
                              // padding: const EdgeInsets.all(8),
                              backgroundColor:
                                  const Color.fromARGB(248, 54, 89, 109),
                              foregroundColor: Colors.white, // icon color
                              elevation: 2,
                              fixedSize: const Size(64, 64),
                            ),
                            onPressed:
                                _nextImageIndex < 3 ? _captureImage : null,
                            child: const Icon(Icons.camera_alt, size: 35),
                          ),
                        ),
                        const SizedBox(width: 50),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            shape: const CircleBorder(),
                            // padding: const EdgeInsets.all(8),
                            backgroundColor:
                                const Color.fromARGB(248, 54, 89, 109),
                            foregroundColor: Colors.white, // icon color
                            elevation: 2,
                            fixedSize: const Size(54, 54),
                          ),
                          onPressed: _capturedImages.any((img) => img != null)
                              ? () async {
                                  await ref
                                      .read(surveyController)
                                      .insertCapturedImages(
                                          context, ref, _capturedImages);
                                  if (clickEventIsFor == 'poleSurvey') {
                                    ref
                                        .read(surveyController)
                                        .insertPoleSurveyDetails(context, ref);
                                  } else if (clickEventIsFor == 'spSurvey') {
                                    ref
                                        .read(surveyController)
                                        .insertSpSurveyDetails(context, ref);
                                  } else if (clickEventIsFor == 'transSurvey') {
                                    ref
                                        .read(surveyController)
                                        .insertTransSurveyDetails(context, ref);
                                  }
                                }
                              : null,
                          child: const Icon(Icons.send, size: 35),
                        ),
                        const SizedBox(width: 20),
                      ],
                    ),
                  ],
                )
              : const Center(child: CircularProgressIndicator()),
        ),
      ),
    );
  }
}
