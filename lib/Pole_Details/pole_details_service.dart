import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:schnell_pole_installation/utils/dio_client.dart';
import '../utils/constants.dart';

class PoleDetailService {
  var status;
  Future<String> pollInstallSucced(context, data) async {
    Dio dio = DioClient.dio;
    try {
      Response response = await dio.post('$baseUrl/api/pole/install/',
          data: data,
          options: Options(contentType: 'application/json', headers: {
            // "token": token,
          }));
      if (response.data != "") {
        var result = jsonDecode(response.data);
        status = result['status'].toString();
      }
      return status;
    } catch (e) {
      log('$e');
      // return 'Something Went Wrong';
      if (e is DioException) {
        if (e.error == 'Session expired. Please login again.') {
          return "401";
        } else {
          return "Server Time Out";
        }
      } else {
        return 'Something Went Wrong';
      }
    }
  }
}
