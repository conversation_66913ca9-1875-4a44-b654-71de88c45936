import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:schnell_pole_installation/Pole_Details/pole_details.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/model_pole_installation.dart';
import 'package:schnell_pole_installation/splash_screen/splash_page.dart';
import 'package:schnell_pole_installation/utils/dio_client.dart';
import 'package:schnell_pole_installation/utils/loader.dart';

import '../utils/constants.dart';

class PoleInstallationService {
  Future<String> pollInstall(
      value, context, token, mode, polecount, specificUserPoleCount) async {
    var result2;
    loaderAnimation('loading...');
    Dio dio = DioClient.dio;
    try {
      Response response = await dio.get(
        '$baseUrl/api/get/pole/?poleNo=$value',
        options: Options(headers: {
          // "token": token,
        }),
      );
      var status = jsonDecode(response.data);
      if (response.data != "") {
        if (status['status'] == 404) {
          EasyLoading.dismiss(animation: true);
          var dateTime = DateTime.now().millisecondsSinceEpoch.toString();
          result2 = '0';
          if (mode == '1') {
            Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                    builder: (context) => PoleDetails(
                        token: token,
                        region: region,
                        zone: zone,
                        ward: ward,
                        poleNumber: value,
                        customerId: customerId,
                        wardId: wardId,
                        installedOn: dateTime,
                        installedBy: userName,
                        poleCount: polecount,
                        specificUserPoleCount: specificUserPoleCount)));
          } else {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => PoleDetails(
                        token: token,
                        region: region,
                        zone: zone,
                        ward: ward,
                        poleNumber: value,
                        customerId: customerId,
                        wardId: wardId,
                        installedOn: dateTime,
                        installedBy: userName,
                        poleCount: polecount,
                        specificUserPoleCount: specificUserPoleCount)));
          }
        } else {
          var result = jsonDecode(response.data);
          if (result['id'] != '') {
            result2 = '1';
            PoleInstallationModel jsonResponce =
                PoleInstallationModel.fromjson(json.decode(response.data));
            poleDetailsData = jsonResponce;
          }
        }
      }
      return result2;
    } catch (e) {
      if (e is DioException) {
        EasyLoading.dismiss(animation: true);
        return alertPopUp(context, 'Server Timeout Please Try Again!',
            'assets/animation/warn.json');
      } else {
        EasyLoading.dismiss(animation: true);
        return alertPopUp(context, 'Something went wrong. Please try Again!',
            'assets/animation/warn.json');
      }
    }
  }
}

PoleInstallationModel? poleDetailsData;
