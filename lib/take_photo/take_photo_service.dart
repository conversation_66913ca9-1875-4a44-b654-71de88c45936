import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/dio_client.dart';

class TakePhotoService {
  var status;
  Future<String> updateImage(
      deviceImage, token, poleNumber, fileName, context) async {
    var data = {
      "name": fileName,
      "activity": "INSTALL",
      "auditImg": deviceImage
    };
    Dio dio = DioClient.dio;
    try {
      Response response = await dio.post('$baseUrl/api/image/upload/',
          data: data,
          options: Options(contentType: 'application/json', headers: {
            // "token": token,
          }));
      if (response.data != "") {
        var result = jsonDecode(response.data);
        status = result['status'].toString();
      } else {
        //  snackBarLoader('No Data Found',context);
      }
      return status;
    } catch (e) {
      return 'Something Went Wrong';
    }
  }
}
