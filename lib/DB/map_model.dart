import 'dart:convert';

import 'package:flutter/material.dart';

class PoleCountModel extends ChangeNotifier {
  String? name;
  String? type;
  String? customerId;
  String? wardId;
  double? latitude;
  double? longitude;
  double? accuracy;
  String? location;
  String? manualEnteredLocation;
  String? state;
  String? wardName;
  String? zoneName;
  String? region;
  int? installedOn;
  String? installedBy;
  Map? clampDimension;
  List<dynamic>? lampProfiles;
  String? connection;
  int? armCount;
  String? uuidFileName;
  String? deviceImage;
  String? imagePoleName;
  String? imageFileName;
  String? poleCountName;
  String? poleCountWardId;
  int? incPoleCount;
  double? wtrmrkLatitude;
  double? wtrmrkLongitude;
  double? wtrmrkAccuracy;
  String? wtrmrkLocation;
  String? wtrmrkManualEnteredLocation;
  String? wtrmrkWard;
  String? wtrmrkZone;
  String? wtrmrkRegion;

  userMap() {
    var mapping = Map<String, dynamic>();
    mapping['name'] = name;
    mapping['type'] = type;
    mapping['customerId'] = customerId;
    mapping['wardId'] = wardId;
    mapping['latitude'] = latitude;
    mapping['longitude'] = longitude;
    mapping['accuracy'] = accuracy;
    mapping['location'] = location;
    mapping['manualEnteredLocation'] = manualEnteredLocation;
    mapping['state'] = state;
    mapping['wardName'] = wardName;
    mapping['zoneName'] = zoneName;
    mapping['region'] = region;
    mapping['installedOn'] = installedOn;
    mapping['installedBy'] = installedBy;
    mapping['clampDimension'] =
        clampDimension != null ? json.encode(clampDimension) : null;
    if (armCount != 0) {
      mapping['lampProfiles'] =
          lampProfiles != null ? json.encode(lampProfiles) : null;
    }
    mapping['connection'] = connection;
    mapping['armCount'] = armCount;
    mapping['uuidFileName'] = uuidFileName;
    return mapping;
  }

  imageMap() {
    var mapping = Map<String, dynamic>();
    mapping['name'] = imagePoleName;
    mapping['deviceImage'] = deviceImage;
    mapping['fileName'] = imageFileName;
    mapping['latitude'] = wtrmrkLatitude;
    mapping['longitude'] = wtrmrkLongitude;
    mapping['accuracy'] = wtrmrkAccuracy;
    mapping['location'] = wtrmrkLocation;
    mapping['manualEnteredLocation'] = wtrmrkManualEnteredLocation;
    mapping['wardName'] = wtrmrkWard;
    mapping['zoneName'] = wtrmrkZone;
    mapping['region'] = wtrmrkRegion;
    return mapping;
  }

  poleCountMap() {
    var mapping = Map<String, dynamic>();
    mapping['name'] = poleCountName;
    mapping['wardId'] = poleCountWardId;
    return mapping;
  }

  incCountMap() {
    var mapping = Map<String, dynamic>();
    mapping['polecount'] = incPoleCount;
    return mapping;
  }

  // datemap(){

  // }
}
