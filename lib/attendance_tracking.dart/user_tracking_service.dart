import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/dio_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

class UserTrackingService {
  Future<dynamic> pushLocationToUserTelemetry() async {
    final sharedPreference = await SharedPreferences.getInstance();
    String? userId = sharedPreference.getString("userId");
    log('get stored userId: $userId');
    var storedLocation = sharedPreference.getStringList("locations");
    Dio dio = DioClient.dio;
    List<String> locationsToRemove = [];

    if (storedLocation != null) {
      for (var location in List.from(storedLocation)) {
        try {
          var decodedLocation = jsonDecode(location);
          final response = await dio.post(
            '$ticketURL/api/plugins/telemetry/USER/$userId/timeseries/ANY',
            options: Options(
              headers: {
                'accept': 'application/json',
                'Content-Type': 'application/json',
                // 'X-Authorization': 'Bearer $token',
              },
            ),
            data: decodedLocation,
          );
          log('Location update response: ${response.statusCode}');
          if (response.statusCode == 200) {
            locationsToRemove.add(location);
            log('Location successfully updated to user telemetry and marked for removal');
          }
        } catch (e) {
          log('$e');
          if (e is DioError) {
            if (e.error == 'Session expired. Please login again.') {
              return 401;
            } else {
              return "Server Time Out";
            }
          } else {
            return "Something Went Wrong";
          }
          // if (e is DioError) {
          //   if (e.error == 'Session expired. Please login again.') {
          //     return 'refresht token expired';
          //   }
          // }
        }
      }
      // Remove successfully sent locations from storedLocation
      storedLocation
          .removeWhere((location) => locationsToRemove.contains(location));
      await sharedPreference.setStringList("locations", storedLocation);
      log("Remaining stored locations: $storedLocation");
      log("*** Locations marked for removal: $locationsToRemove");
    }
  }
}
