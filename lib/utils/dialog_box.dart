import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:lottie/lottie.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/pole_installation.dart';
import 'package:schnell_pole_installation/survey/pole_survey.dart';
import 'package:schnell_pole_installation/utils/constants.dart';

requestLocation(context) {
  return QuickAlert.show(
    context: context,
    type: QuickAlertType.info,
    title: 'Location Turned Off!',
    // showCancelBtn: true,
    showConfirmBtn: true,
    // textTextStyle: const TextStyle(color: Colors.black),
    text: 'Request to Enable Location Service',
    // lottieAsset: 'assets/animation/userAlert.json',

    backgroundColor: const Color.fromARGB(255, 230, 218, 242),
    onConfirmBtnTap: () async {
      await Geolocator.requestPermission();
    },
  );
}

void showToastMessage(BuildContext context, String msg) {
  SnackBar snackBar = SnackBar(
    content: Text(msg),
    backgroundColor: Colors.red,
    behavior: SnackBarBehavior.floating,
  );
  ScaffoldMessenger.of(context).showSnackBar(snackBar);
}

warningPopup(BuildContext context, String msg) async {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Lottie.asset('assets/animation/userAlert.json',
                  height: 150, width: 150),
              const SizedBox(height: 20),
              Text(msg, textAlign: TextAlign.center),
            ],
          ),
          actions: <Widget>[
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              TextButton(
                onPressed: () {
                  Navigator.of(context, rootNavigator: true).pop();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const PoleInstallation()),
                  );
                  // Navigator.pushAndRemoveUntil(
                  //     context,
                  //     MaterialPageRoute(
                  //         builder: (context) => const PoleInstallation()),
                  //     (Route<dynamic> route) => false);
                },
                child: Text("Installation", style: TextStyle(color: bDarkBlue)),
              ),
              const SizedBox(width: 50),
              OutlinedButton(
                style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 30),
                    side: BorderSide(color: bDarkBlue),
                    backgroundColor: bDarkBlue),
                onPressed: () async {
                  Navigator.of(context, rootNavigator: true).pop();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const PoleSurveyInformation()),
                  );
                  //   Navigator.pushAndRemoveUntil(
                  //       context,
                  //       MaterialPageRoute(
                  //           builder: (context) => const BaseSurveyInformation()),
                  //       (Route<dynamic> route) => false);
                },
                child: Text(
                  "Survey",
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
              ),
            ]),
          ],
        ),
      );
    },
  );
}
