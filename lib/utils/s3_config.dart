// class S3Config {
//   static const String bucketName = 'schnell-s3-image';
//   static const String region = 'ap-south-1';
//   static const String accessKey = '********************';
//   static const String secretKey = 't99dd9UHF3h4OVvxbosN+JnJIsaD4jd/d+7JNIER';
//   static const String luminatorFolder = 'luminator';
//   static const String ppeImageFolder = 'ppeImages';
//   static const String grievanceFolder = 'grievances';
// }

class S3Config {
  static const String bucketName = 'luminator-iotpro';
  static const String region = 'us-east-1';
  static const String accessKey = '********************';
  static const String secretKey = 'wLtFR5vFUs1q0xfpWs8JaqTPVefxhvNnPl9E04uR';
  static const String luminatorFolder = 'luminator';
  static const String ppeImageFolder = 'ppeImages';
  static const String grievanceFolder = 'grievances';
}
// AWS_BUCKET_TYPE=s3
// AWS_BUCKET_NAME=luminator-iotpro
// AWS_BUCKET_REGION=us-east-1
// AWS_BUCKET_ACCESS_KEY=********************
// AWS_BUCKET_SECRET_KEY=wLtFR5vFUs1q0xfpWs8JaqTPVefxhvNnPl9E04uR
// AWS_BUCKET_FOLDER_NAME=luminator
// AWS_BUCKET_LUMINATOR_FOLDER_NAME=luminator
// AWS_BUCKET_GRIEVANCE_FOLDER_NAME=grievances
