import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DioClient {
  static final Dio dio = Dio()
    ..interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final prefs = await SharedPreferences.getInstance();
        String? accessToken = prefs.getString('token');
        String? refreshToken = prefs.getString('refreshToken');

        final now = DateTime.now().millisecondsSinceEpoch;
        final expiry = await _getJwtExpiry(accessToken);

        // If token expired or about to expire
        if (expiry != null && expiry < now + 30000) {
          try {
            var headers = {
              'X-Authorization': 'Bearer $refreshToken',
            };
            var data = json.encode({"refreshToken": refreshToken});
            // Use a new Dio instance WITHOUT interceptor
            final tempDio = Dio();
            final response = await tempDio.post(
              '$ticketURL/api/auth/token',
              options: Options(
                headers: headers,
              ),
              data: data,
            );
            if (response.statusCode == 200) {
              log(json.encode(response.data));
              accessToken = response.data['token'];
              refreshToken = response.data['refreshToken'];

              await prefs.setString('token', accessToken!);
              await prefs.setString('refreshToken', refreshToken!);
            } else if (response.statusCode == 401) {
              return handler.reject(
                DioException(
                  requestOptions: options,
                  error: 'Session expired. Please login again.',
                ),
              );
            }
          } catch (e) {
            return handler.reject(
              DioException(
                requestOptions: options,
                error: 'Session expired. Please login again.',
              ),
            );
          }
        }

        if (accessToken != null) {
          options.headers['X-Authorization'] = 'Bearer $accessToken';
          options.headers['token'] = accessToken;
        }

        return handler.next(options);
      },
    ));

  // Decode JWT and extract expiry
  static Future<int?> _getJwtExpiry(String? token) async {
    try {
      final parts = token?.split('.');
      if (parts == null || parts.length != 3) return null;
      final payload =
          utf8.decode(base64Url.decode(base64Url.normalize(parts[1])));
      final Map<String, dynamic> data = json.decode(payload);
      return (data['exp'] as int?)!.toInt() * 1000;
    } catch (_) {
      return null;
    }
  }
}
