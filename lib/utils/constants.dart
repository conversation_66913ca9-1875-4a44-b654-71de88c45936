import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:google_fonts/google_fonts.dart';

String appName = 'PoleVault';
const appVersion = "2.2.0";
const splashRoute = '/';
const dashboardRoute = '/pollInstallation';
const updateRoute = '/updatePage';
const qrRoute = '/poleDetails';
const photoRoute = '/photo';
const snacBarTitle = "You seem Offline!";
const snackBarMessage = 'Please check your connection.';
const displayPhotoRoute = '/displayPhoto';
Color bDarkBlue = const Color.fromARGB(248, 32, 61, 78);
Color darkBlue = const Color(0xF8407CA1).withOpacity(0.8);
Color lightBlue = const Color.fromARGB(248, 64, 124, 161).withOpacity(0.19);
const secondaryColor = Color.fromARGB(248, 64, 124, 161);
const bgColor = Color.fromARGB(248, 64, 124, 161);
const primaryColor = Color(0xFF2697FF);
const defaultPadding = 16.0;
const logoImage = "assets/images/logo.png";
const qrImage = "assets/images/qrcode.png";
const customShape = 'assets/images/customShape.png';
const fullcustomShape = 'assets/images/fullCustomShape.png';
const topcustomShape = 'assets/images/topCustomShape.png';
const bottomcustomShape = 'assets/images/buttomCustomShape.png';
const img1 = 'assets/images/pole1.png';
const img2 = 'assets/images/pole2.png';
const img3 = 'assets/images/pole3.png';

// String baseUrl = 'https://api.schnelliot.in';
// String ticketURL = 'https://schnelliot.in';

String baseUrl = 'https://api.iotpro.io';
String ticketURL = 'https://iotpro.io';

const roadTypes = ["Main Road", "Service Road", "Bridge Road"];
const assetTypes = ["Switch Point", "Pole", "Transformer"];
const poleTypes = [
  'RCC',
  'Tubular',
  'Post Top',
  'Octagonal',
  'High Mast(HM)',
  'Mini Mast(MM)',
  'Metal',
  'Spun',
  'Others'
];

List<String> poleHeightOptions = [
  '7M',
  '8M',
  '9M',
  '10M',
  '12M',
  '16M',
  '20M',
  'Others',
];

const poleSpan = [
  'Less than 20M',
  '20M',
  '25M',
  '30M',
  '35M',
  '40M',
  'More than 40M',
];

const poleConditions = ['Good', 'Bad', 'Missing'];

// Sample lamp types and their corresponding wattages
final List<String> lampTypes = [
  'Lamp Type',
  'Tube light',
  'SVL',
  'CFL',
  'LED',
  'T5',
  'BULB'
];

final Map<String, List<String>> lampWattages = {
  'Lamp Type': ['Lamp Wattage'],
  'Tube light': [
    '40 W',
  ],
  'SVL': [
    '150 W',
    '250 W',
    '400 W',
  ],
  'CFL': [
    '25 W',
    '36 W',
    '65 W',
    '72 W',
    '85 W',
  ],
  'LED': [
    '20 W',
    '40 W',
    '60 W',
    '70 W',
    '90 W',
    '120 W',
    '150 W',
    '200 W',
  ],
  'T5': [
    '96 W',
    '120 W',
    '220 W',
  ],
  'BULB': ['10 W'],
};

List<Map<String, String>> selectedLamps = [
  {
    'type': 'Lamp Type',
    'watts': 'Lamp Wattage',
  },
];

const incomingTransLineOptions = [
  'Under Ground',
  'Conductor',
  'Overhead',
  'Missing',
  'ABC',
];

const incomingTransTypeOptions = [
  'Dedicated for Streetlight',
  'Taping from CommonLine',
];

const bracketMountingHeightOptions = [
  'Not Required',
  '6m',
  '7m',
  '8m',
  '9m',
  '12m',
  '16m',
  'Others'
];

const manualSwitchControlOptions = [
  'Exists - Good Condition',
  'Exists - Bad Condition',
  'Exists - Not working',
  'Not Exists'
];

Map<String, List<String>> lampWattageOptions = {
  'Tube light': ['40 W'],
  'SVL': ['150 W', '250 W', '400 W'],
  'CFL': ['25 W', '36 W', '65 W', '72 W', '85 W'],
  'LED': ['20 W', '40 W', '60 W', '70 W', '90 W', '120 W', '150 W', '200 W'],
  'T5': ['96 W', '120 W', '220 W'],
  'BULB': ['10 W'],
};

const clampTypeOptions = [
  'Clamp Type Not Required',
  '6 x 3 inch',
  '7 x 3 inch',
  '8 x 4 inch',
  '11 x 3 inch',
  '12 x 4 inch',
  'Others',
];

List<String> armCountOptions = ['0', '1', '2', '4', '8', '12'];

final Map<String, List<String>> roadTypeOptions = {
  'A1': ['Main Road', 'Bridge'],
  'A2': ['Main Road', 'Bridge'],
  'B1': ['Service Road'],
  'B2': ['Service Road'],
  'Park': ['Park Road'],
};

final Map<String, List<String>> roadWidthOptions = {
  'Main Road': ['above 9m', '7m-9m'],
  'Bridge': ['above 9m', '7m-9m'],
  'Service Road': ['upto 5m', '5m-7m'],
  'Park Road': ['others'],
};

final Map<String, List<String>> vehicleAccessOptions = {
  'above 9m': ['Long Truck'],
  '7m-9m': ['mini Truck'],
  '5m-7m': ['Jeep/Bada Dosth'],
  'upto 5m': ['Tata Ace', 'Narrower than Tata Ace'],
  'others': ['Tata Ace'],
};

List<String> units = ['cm', 'inch', 'mm'];

const switchPointTypeOptions = ['Feeder Pillar', 'HRC Fuse', 'Timer Box'];
const sPMeterOptions = ['Working', 'Not Working'];
const spConditionOptions = ['Working', 'Not Working'];
const spEarthingConditionOptions = ['Good', 'Bad', 'N.A'];
const spMeterTypeOptions = ['Digital', 'Analogue', 'DP/Manual'];
const spPhaseOptions = ['1', '3'];
const spMakeOptions = ['L&T', 'Others'];

const poleMotorRatingUnitsOptions = ['hp', 'kW'];
const poleWinchConditionOptions = ['Good', 'Faulty'];
const poleRopeConditionOptions = ['Working', 'Not Working'];
const motorConditionOptions = ['Good', 'Bad', 'Missing'];

const spinkit = SpinKitThreeBounce(
  color: Color.fromARGB(248, 64, 124, 161),
  size: 30.0,
);
const loginLoader = SpinKitRing(
  color: Color.fromARGB(248, 64, 124, 161),
  size: 30.0,
);

ThemeData lightTheme(context) => ThemeData(
      useMaterial3: false,
      scaffoldBackgroundColor: Colors.white,
      textTheme:
          GoogleFonts.poppinsTextTheme(Theme.of(context).textTheme.copyWith(
                headlineSmall: TextStyle(color: Colors.grey[800]),
                titleLarge: const TextStyle(color: Colors.grey),
                bodyLarge: const TextStyle(color: Colors.grey),
                bodySmall: const TextStyle(color: Colors.black),
                titleMedium: TextStyle(color: Colors.grey[400]),
                titleSmall: TextStyle(color: Colors.grey[200]),
                bodyMedium: const TextStyle(color: Colors.black),
              )),
      canvasColor: Colors.grey[400],
      primaryColor: Colors.white,
      focusColor: Colors.amber[800],
      cardColor: Colors.red.shade800,
      highlightColor: Colors.grey,
      secondaryHeaderColor: Colors.cyan,
      hoverColor: Colors.blueGrey,
      indicatorColor: Colors.pinkAccent[400],
      shadowColor: Colors.lightGreen[400],
      bottomAppBarTheme: BottomAppBarTheme(
        color: Colors.blueAccent[400],
      ),
    );

ThemeData darkTheme(context) => ThemeData(
      scaffoldBackgroundColor: bgColor,
      textTheme:
          GoogleFonts.poppinsTextTheme(Theme.of(context).textTheme.copyWith(
                headlineSmall: const TextStyle(color: Colors.grey),
                titleLarge: const TextStyle(color: Colors.grey),
                bodyLarge: const TextStyle(color: Colors.grey),
                bodySmall: const TextStyle(color: Colors.white),
                titleMedium: TextStyle(color: Colors.grey[400]),
                titleSmall: TextStyle(color: Colors.grey[200]),
                bodyMedium: const TextStyle(color: Colors.white),
              )),
      canvasColor: secondaryColor,
      primaryColor: Colors.black,
      focusColor: Colors.amber[800],
      cardColor: Colors.red.shade800,
      highlightColor: Colors.blueGrey[400],
      secondaryHeaderColor: Colors.teal[800],
      hoverColor: Colors.blueGrey,
      indicatorColor: Colors.pinkAccent[400],
    );

class LumeAppTheme {
  LumeAppTheme._();
  static const Color nearlyWhite = Color(0xFFFAFAFA);
  static const Color white = Color(0xFFFFFFFF);
  static const Color background = Color(0xFFF2F3F8);
  static const Color nearlyDarkBlue = Color(0xFF2633C5);

  static const Color nearlyBlue = Color(0xFF00B6F0);
  static const Color nearlyBlack = Color(0xFF213333);
  static const Color grey = Color(0xFF3A5160);
  static const Color darkGrey = Color(0xFF313A44);

  static const Color darkText = Color(0xFF253840);
  static const Color darkerText = Color(0xFF17262A);
  static const Color lightText = Color(0xFF4A6572);
  static const Color deactivatedText = Color(0xFF767676);
  static const Color dismissibleBackground = Color(0xFF364A54);
  static const Color spacer = Color(0xFFF2F2F2);
  static const String fontName = 'Roboto';

  static const TextStyle display1 = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.bold,
    fontSize: 36,
    letterSpacing: 0.4,
    height: 0.9,
    color: darkerText,
  );

  static const TextStyle headline = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.bold,
    fontSize: 24,
    letterSpacing: 0.27,
    color: darkerText,
  );

  static const TextStyle title = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.bold,
    fontSize: 16,
    letterSpacing: 0.18,
    color: darkerText,
  );

  static const TextStyle subtitle = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.w400,
    fontSize: 14,
    letterSpacing: -0.04,
    color: darkText,
  );

  static const TextStyle body2 = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.w400,
    fontSize: 14,
    letterSpacing: 0.2,
    color: darkText,
  );

  static const TextStyle body1 = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.w400,
    fontSize: 16,
    letterSpacing: -0.05,
    color: darkText,
  );

  static const TextStyle caption = TextStyle(
    fontFamily: fontName,
    fontWeight: FontWeight.w400,
    fontSize: 12,
    letterSpacing: 0.2,
    color: lightText, // was lightText
  );
}
