import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:schnell_pole_installation/Dashboard/dashboard.dart';


class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();
   int _selectedIndex = 0;
   navigateBar(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  final List<Widget> body = [
    const Dashboard(),
    // const MapPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: WillPopScope(
        onWillPop: () {
          return showExitPopup(context);
        },
        child: Scaffold(
          key: _scaffoldKey,
          appBar: AppBar(
              leading: Builder(builder: (BuildContext context) {
                return IconButton(
                  icon: const Icon(
                    Icons.menu,
                    color: Color.fromARGB(248, 32, 61, 78),
                  ),
                  onPressed: () {
                    Scaffold.of(context).openDrawer();
                  },
                );
              }),
              backgroundColor:
                  const Color.fromARGB(248, 64, 124, 161).withOpacity(0.19),
              elevation: 0.0,
              centerTitle: true,
              titleTextStyle: TextStyle(
                color: Theme.of(context).textTheme.bodySmall!.color,
                fontSize: 18.0,
              ),
              title: Text(
                _selectedIndex == 0 ? "Home Page" : "Map View",
                style: const TextStyle(color: Color.fromARGB(248, 32, 61, 78)),
              ),
              actions: <Widget>[
                IconButton(
                  icon: Icon(Icons.search,
                      color: Theme.of(context).textTheme.bodySmall!.color),
                  onPressed: () {
                  },
                ),
              ]),
          body: body.elementAt(_selectedIndex),
          bottomNavigationBar: BottomNavigationBar(
            backgroundColor: const Color.fromARGB(248, 217, 232, 243),
            type: BottomNavigationBarType.fixed,
            currentIndex: _selectedIndex,
            selectedItemColor: const Color.fromARGB(248, 32, 61, 78),
            unselectedItemColor:
                Theme.of(context).textTheme.bodySmall!.color!.withOpacity(.50),
            selectedFontSize: 12,
            unselectedFontSize: 12,
            onTap: navigateBar,
            items: const [
              BottomNavigationBarItem(
                label: 'Home',
                icon: Icon(Icons.home),
              ),
              BottomNavigationBarItem(label: 'Map', icon: Icon(Icons.map)),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> showExitPopup(context) async {
    var bg = const Color.fromARGB(255, 200, 220, 236);
    return await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: bg,
            content: Container(
              height: 100,
              color: bg,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Do you want to exit?",
                    style: TextStyle(color: Colors.black),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            if (kDebugMode) {
                              log('yes selected');
                            }
                            exit(0);
                          },
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red),
                          child: const Text(
                            "Yes",
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                          child: ElevatedButton(
                        onPressed: () {
                          if (kDebugMode) {
                            log('no selected');
                          }
                          Navigator.of(context).pop();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).textTheme.bodySmall!.color,
                        ),
                        child: const Text("No",
                            style: TextStyle(color: Colors.white)),
                      ))
                    ],
                  )
                ],
              ),
            ),
          );
        });
  }
}