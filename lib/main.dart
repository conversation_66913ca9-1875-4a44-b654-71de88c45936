import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as r;
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/DB/map_model.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/pole_installation.dart';
import 'package:schnell_pole_installation/Update_Page.dart/update_page.dart';
import 'package:schnell_pole_installation/splash_screen/splash_page.dart';
import 'package:schnell_pole_installation/utils/constants.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  HttpOverrides.global = MyHttpOverrides();
  await Hive.initFlutter(); // from hive_flutter package

  runApp(r.ProviderScope(
    child: MultiProvider(providers: [
      ChangeNotifierProvider<PoleCountModel>(create: (_) => PoleCountModel()),
      ChangeNotifierProvider<DataModel>(create: (_) => DataModel()),
    ], child: const MyApp()),
  ));
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'PoleVault',
      debugShowCheckedModeBanner: false,
      theme: lightTheme(context),
      // home:const SplashPage(),
      builder: EasyLoading.init(),
      initialRoute: splashRoute,
      routes: {
        splashRoute: (context) => const SplashPage(),
        updateRoute: (context) => const UpdatePage(),
        // dashboardRoute :(context) => PoleInstallation(token: token, region: region, zone: zone, ward: ward, wardId: wardId, userName: userName, customerId: customerId)
        dashboardRoute: (context) => const PoleInstallation(),
        // splashRoute :(context) => PoleDetails(token: 'token',
        //             region: 'region',
        //             zone: 'zone',
        //             ward: 'ward',
        //             poleNumber: 'value',
        //             customerId: 'customerId',
        //             wardId: 'wardId',
        //             installedOn: 'dateTime',
        //             installedBy: 'userName',
        //             poleCount : 'polecount')
      },
    );
  }
}
