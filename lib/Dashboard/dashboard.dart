import 'package:flutter/material.dart';

import '../utils/box_container.dart';

class Dashboard extends StatelessWidget {
  const Dashboard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: <PERSON>umn(children: [
        const SizedBox(
          height: 8,
        ),
        GestureDetector(
          child: BoxContainer.rectangleContainer('TUP-TUP-1-TUP-1-1'),
          onTap: () {},
        ),
        const SizedBox(
          height: 30,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            GestureDetector(
              // onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context)=>const PoleInstallation())),
              child: BoxContainer.boxContainer(context, 'Pole'),
            ),
            GestureDetector(
              child: BoxContainer.boxContainer(context, 'Light\nPoint'),
            ),
          ],
        ),
        const SizedBox(
          height: 30,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            GestureDetector(
              child: BoxContainer.boxContainer(context, 'CCMS'),
            ),
            GestureDetector(
              child: BoxContainer.boxContainer(context, 'HUB'),
            ),
          ],
        )
      ]),
    );
  }
}
