{"buildFiles": ["/home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>-<PERSON>ault/android/app/.cxx/Debug/1h3k613i/x86", "clean"]], "buildTargetsCommandComponents": ["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>-<PERSON>ault/android/app/.cxx/Debug/1h3k613i/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/linux-x86_64/bin/clang.lld", "cppCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}