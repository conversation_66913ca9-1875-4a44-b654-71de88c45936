                        -H/home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=27
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-27
-D<PERSON>DROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/27.0.12077973
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/Akshitha/<PERSON><PERSON><PERSON>-<PERSON>Vault/build/app/intermediates/cxx/Debug/1h3k613i/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/Akshitha/Schnell-PoleVault/build/app/intermediates/cxx/Debug/1h3k613i/obj/x86_64
-DCMAKE_BUILD_TYPE=Debug
-B/home/<USER>/Akshitha/Schnell-PoleVault/android/app/.cxx/Debug/1h3k613i/x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2