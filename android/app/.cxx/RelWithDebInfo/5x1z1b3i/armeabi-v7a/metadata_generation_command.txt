                        -H/home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=27
-DANDROID_PLATFORM=android-27
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/27.0.12077973
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/Akshitha/Schnell-PoleVault/build/app/intermediates/cxx/RelWithDebInfo/5x1z1b3i/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/Akshitha/Schnell-PoleVault/build/app/intermediates/cxx/RelWithDebInfo/5x1z1b3i/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/home/<USER>/Akshitha/Schnell-PoleVault/android/app/.cxx/RelWithDebInfo/5x1z1b3i/armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2