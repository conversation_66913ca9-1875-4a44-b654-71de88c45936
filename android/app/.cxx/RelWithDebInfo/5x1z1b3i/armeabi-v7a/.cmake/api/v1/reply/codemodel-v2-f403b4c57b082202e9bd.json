{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/home/<USER>/<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>-<PERSON>ault/android/app/.cxx/RelWithDebInfo/5x1z1b3i/armeabi-v7a", "source": "/home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}